package com.adins.esign.dataaccess.impl;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.dataaccess.api.UserActivityLogDao;
import com.adins.esign.model.TrUserActivityLog;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class UserActivityLogDaoHbn extends BaseDaoHbn implements UserActivityLogDao {
	
	@Override
	public void insertUserActivityLog(TrUserActivityLog userActivityLog) {
		userActivityLog.setUsrCrt(MssTool.maskData(userActivityLog.getUsrCrt()));
		this.managerDAO.insert(userActivityLog);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertUserActivityLogNewTrx(TrUserActivityLog userActivityLog) {
		userActivityLog.setUsrCrt(MssTool.maskData(userActivityLog.getUsrCrt()));
		this.managerDAO.insert(userActivityLog);
	}
}
